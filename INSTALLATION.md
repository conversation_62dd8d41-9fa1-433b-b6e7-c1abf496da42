# 夸地科技官网安装指南

## 🚀 快速开始

### 第一步：安装 Node.js

#### macOS 用户
```bash
# 方法1: 使用 Homebrew (推荐)
brew install node

# 方法2: 从官网下载
# 访问 https://nodejs.org/ 下载 LTS 版本
```

#### Windows 用户
```bash
# 方法1: 使用 Chocolatey
choco install nodejs

# 方法2: 从官网下载
# 访问 https://nodejs.org/ 下载 LTS 版本
```

#### Linux 用户
```bash
# Ubuntu/Debian
curl -fsSL https://deb.nodesource.com/setup_lts.x | sudo -E bash -
sudo apt-get install -y nodejs

# CentOS/RHEL
curl -fsSL https://rpm.nodesource.com/setup_lts.x | sudo bash -
sudo yum install -y nodejs
```

### 第二步：验证安装
```bash
node --version  # 应该显示 v18.0.0 或更高版本
npm --version   # 应该显示 npm 版本
```

### 第三步：安装项目依赖
```bash
# 在项目根目录运行
npm install
```

### 第四步：启动开发服务器
```bash
npm run dev
```

### 第五步：访问网站
打开浏览器访问: http://localhost:3000

## 🔧 故障排除

### 常见问题

#### 1. npm install 失败
```bash
# 清除缓存
npm cache clean --force

# 删除 node_modules 重新安装
rm -rf node_modules package-lock.json
npm install
```

#### 2. 端口被占用
```bash
# 使用不同端口
npm run dev -- -p 3001
```

#### 3. TypeScript 错误
```bash
# 重新生成类型文件
npm run build
```

## 📱 预览效果

启动成功后，您将看到：

1. **首页**: 展示公司使命愿景和产品预览
2. **关于我们**: 详细的公司介绍和价值观
3. **产品页面**: 兰台系列和UnMiss产品展示
4. **联系我们**: 公司联系信息

## 🌍 多语言测试

网站会根据浏览器语言自动切换，您也可以手动测试：

- 中文: http://localhost:3000/zh
- 英文: http://localhost:3000/en
- 日文: http://localhost:3000/ja
- 韩文: http://localhost:3000/ko
- 西班牙文: http://localhost:3000/es
- 法文: http://localhost:3000/fr

## 🚀 部署到生产环境

### Vercel 部署 (推荐)
1. 注册 Vercel 账号: https://vercel.com
2. 连接 GitHub 仓库
3. 导入项目
4. 自动部署完成

### 其他部署选项
- **Netlify**: 支持静态导出
- **AWS Amplify**: 全托管部署
- **自建服务器**: 使用 PM2 或 Docker

## 📞 技术支持

如果遇到问题，请联系：
- 邮箱: <EMAIL>
- 查看 README.md 获取更多信息
