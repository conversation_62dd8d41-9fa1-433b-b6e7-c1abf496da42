'use client';

import React from 'react';
import { useTranslations, useLocale } from 'next-intl';
import { motion } from 'framer-motion';
import { Mail, MessageCircle, Clock, Shield } from 'lucide-react';
import Card from '@/components/ui/Card';
import Button from '@/components/ui/Button';

const ContactPage: React.FC = () => {
  const t = useTranslations();
  const locale = useLocale();

  // 判断是否为中文版本
  const isChinese = locale === 'zh-CN' || locale === 'zh-HK';

  return (
    <div className="pt-16">
      {/* Hero Section */}
      <section className="py-20 bg-gradient-to-br from-primary-50 to-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center"
          >
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              {t('contact.title')}
            </h1>
            <p className={`text-xl text-gray-600 max-w-3xl mx-auto mb-8 ${isChinese ? 'text-center' : 'text-justify'}`}>
              {t('contact.subtitle')}
            </p>
            <div className="inline-flex items-center space-x-2 bg-primary-600 text-white px-6 py-3 rounded-full">
              <Mail size={20} />
              <span className="font-medium">{t('contact.emailPrimary')}</span>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Main Contact Section */}
      <section className="py-20">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Email Contact Card */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="mb-16"
          >
            <Card gradient className="text-center">
              <div className="w-20 h-20 bg-primary-600 rounded-full flex items-center justify-center mx-auto mb-8">
                <Mail size={40} className="text-white" />
              </div>
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                {t('contact.emailContact.title')}
              </h2>
              <p className={`text-lg text-gray-600 mb-8 max-w-2xl mx-auto ${isChinese ? 'text-center' : 'text-left'}`}>
                {t('contact.emailContact.description')}
              </p>
              <div className="bg-white rounded-lg p-6 mb-8 border border-gray-100">
                <a
                  href="mailto:<EMAIL>"
                  className="text-2xl font-bold text-primary-600 hover:text-primary-700 transition-colors"
                >
                  <EMAIL>
                </a>
              </div>
              <Button
                size="lg"
                onClick={() => window.location.href = 'mailto:<EMAIL>'}
                className="group"
              >
                <Mail size={20} className="mr-2" />
                {t('contact.emailContact.button')}
              </Button>
            </Card>
          </motion.div>

          {/* Contact Guidelines */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
          >
            <h3 className="text-2xl font-bold text-gray-900 mb-8 text-center">
              {t('contact.guidelines.title')}
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <Card className="text-center">
                <div className="w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <MessageCircle size={24} className="text-primary-600" />
                </div>
                <h4 className="text-lg font-semibold text-gray-900 mb-2">
                  {t('contact.guidelines.content.title')}
                </h4>
                <p className={`text-gray-600 text-sm ${isChinese ? 'text-center' : 'text-left'}`}>
                  {t('contact.guidelines.content.description')}
                </p>
              </Card>

              <Card className="text-center">
                <div className="w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Clock size={24} className="text-primary-600" />
                </div>
                <h4 className="text-lg font-semibold text-gray-900 mb-2">
                  {t('contact.guidelines.response.title')}
                </h4>
                <p className={`text-gray-600 text-sm ${isChinese ? 'text-center' : 'text-left'}`}>
                  {t('contact.guidelines.response.description')}
                </p>
              </Card>

              <Card className="text-center">
                <div className="w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Shield size={24} className="text-primary-600" />
                </div>
                <h4 className="text-lg font-semibold text-gray-900 mb-2">
                  {t('contact.guidelines.privacy.title')}
                </h4>
                <p className={`text-gray-600 text-sm ${isChinese ? 'text-center' : 'text-left'}`}>
                  {t('contact.guidelines.privacy.description')}
                </p>
              </Card>
            </div>
          </motion.div>
        </div>
      </section>




    </div>
  );
};

export default ContactPage;
