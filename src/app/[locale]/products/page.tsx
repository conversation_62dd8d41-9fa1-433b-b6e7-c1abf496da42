'use client';

import React from 'react';
import { useTranslations, useLocale } from 'next-intl';
import { motion } from 'framer-motion';
import { ArrowRight, Calendar, Star, ExternalLink } from 'lucide-react';
import Button from '@/components/ui/Button';
import Card from '@/components/ui/Card';
import { handleProductNavigation, getProductStatusText } from '@/lib/products';

const ProductsPage: React.FC = () => {
  const t = useTranslations();
  const locale = useLocale();

  // 判断是否为中文版本
  const isChinese = locale === 'zh-CN' || locale === 'zh-HK';

  const handleUnMissClick = () => {
    handleProductNavigation('unmiss');
  };

  return (
    <div className="pt-16">
      {/* Hero Section */}
      <section className="py-20 bg-gradient-to-br from-primary-50 to-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center"
          >
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              {t('products.title')}
            </h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              {t('products.subtitle')}
            </p>
          </motion.div>
        </div>
      </section>

      {/* Lantai Series */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Lantai Series Header */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              {t('products.lantai.title')}
            </h2>
            <p className="text-xl text-primary-600 font-medium mb-6">
              {t('products.lantai.subtitle')}
            </p>
            <p className={`text-gray-600 max-w-3xl mx-auto ${isChinese ? 'text-center' : 'text-left'}`}>
              {t('products.lantai.description')}
            </p>
          </motion.div>

          {/* UnMiss Product Detail */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center mb-16">
            <motion.div
              initial={{ opacity: 0, x: -30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
            >
              <div className="relative">
                <div className="absolute top-0 right-0">
                  <span className="bg-primary-600 text-white px-4 py-2 rounded-full text-sm font-medium">
                    {t('products.status.inPreparation')}
                  </span>
                </div>
                <h3 className="text-2xl md:text-3xl font-bold text-gray-900 mb-4 mt-8">
                  UnMiss
                </h3>
                <p className="text-lg text-primary-600 font-medium mb-4">
                  {t('products.unmiss.subtitle')}
                </p>
                <p className="text-gray-600 mb-8 text-left">
                  {t('products.sections.unmissDetail.description')}
                </p>
                <Button
                  size="lg"
                  className="group"
                  onClick={handleUnMissClick}
                >
                  {t('products.unmiss.cta')}
                  <ArrowRight size={20} className="ml-2 group-hover:translate-x-1 transition-transform" />
                </Button>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
            >
              <Card gradient className="relative overflow-hidden">
                <div className="absolute inset-0 bg-gradient-to-br from-primary-600/10 to-primary-700/5"></div>
                <div className="relative z-10 p-8">
                  <div className="w-full h-64 bg-gradient-to-br from-primary-600 to-primary-700 rounded-lg flex items-center justify-center mb-6">
                    <div className="text-white text-center">
                      <div className="text-4xl font-bold mb-2">UnMiss</div>
                      <div className="text-lg opacity-90">{t('products.sections.unmissDetail.previewTitle')}</div>
                      <div className="text-sm opacity-75 mt-2">2025-Q3</div>
                    </div>
                  </div>
                  <div className={isChinese ? 'text-center' : 'text-left'}>
                    <p className="text-gray-600">
                      {t('products.sections.unmissDetail.previewDescription')}
                    </p>
                  </div>
                </div>
              </Card>
            </motion.div>
          </div>

          {/* Other Lantai Products */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <motion.div
              initial={{ opacity: 0, x: -30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
            >
              <div className="relative">
                <div className="absolute top-0 right-0">
                  <span className="bg-gray-400 text-white px-4 py-2 rounded-full text-sm font-medium">
                    {t('products.status.inPlanning')}
                  </span>
                </div>
                <h3 className="text-2xl md:text-3xl font-bold text-gray-900 mb-4 mt-8">
                  {t('products.sections.lantaiOtherProducts.title')}
                </h3>
                <p className="text-lg text-gray-600 font-medium mb-4">
                  {t('products.sections.lantaiOtherProducts.subtitle')}
                </p>
                <p className="text-gray-600 mb-8 text-left">
                  {t('products.sections.lantaiOtherProducts.description')}
                </p>
                <Button variant="outline" disabled>
                  {t('products.status.inPlanning')}
                </Button>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
            >
              <Card className="relative overflow-hidden">
                <div className="absolute inset-0 bg-gradient-to-br from-gray-100 to-gray-50"></div>
                <div className="relative z-10 p-8">
                  <div className="w-full h-64 bg-gradient-to-br from-gray-400 to-gray-500 rounded-lg flex items-center justify-center mb-6">
                    <div className="text-white text-center">
                      <div className="text-3xl font-bold mb-2">{t('products.sections.lantaiOtherProducts.cardTitle')}</div>
                      <div className="text-lg opacity-90">{t('products.sections.lantaiOtherProducts.cardSubtitle')}</div>
                      <div className="text-sm opacity-75 mt-2">{t('products.status.inPlanning')}</div>
                    </div>
                  </div>
                  <div className="text-center">
                    <p className="text-gray-600">
                      {t('products.sections.lantaiOtherProducts.cardDescription')}
                    </p>
                  </div>
                </div>
              </Card>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Coming Soon */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
              {t('products.sections.moreProducts.title')}
            </h2>
            <p className={`text-xl text-gray-600 mb-8 max-w-3xl mx-auto ${isChinese ? 'text-center' : 'text-left'}`}>
              {t('products.sections.moreProducts.description')}
            </p>
            <div className="inline-flex items-center space-x-2 text-primary-600">
              <Calendar size={20} />
              <span className="font-medium">{t('products.status.updating')}</span>
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  );
};

export default ProductsPage;
