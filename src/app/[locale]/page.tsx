import React from 'react';
import { Metadata } from 'next';
import { setRequestLocale } from 'next-intl/server';
import { generatePageMetadata } from '@/lib/seo';
import HomePage from '@/components/pages/HomePage';

export async function generateMetadata({ params }: { params: { locale: string } }): Promise<Metadata> {
  return generatePageMetadata('home', params.locale);
}

export default function Page({ params }: { params: { locale: string } }) {
  setRequestLocale(params.locale);
  return <HomePage />;
}

