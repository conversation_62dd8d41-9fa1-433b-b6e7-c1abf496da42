'use client';

import React from 'react';
import { useTranslations, useLocale } from 'next-intl';
import { motion } from 'framer-motion';
import { Target, Sparkles, Globe, Users, Lightbulb, Heart } from 'lucide-react';
import Card from '@/components/ui/Card';

const AboutPage: React.FC = () => {
  const t = useTranslations();
  const locale = useLocale();

  // 判断是否为中文版本
  const isChinese = locale === 'zh-CN' || locale === 'zh-HK';

  const values = [
    {
      icon: Heart,
      title: t('about.values.items.0.title'),
      description: t('about.values.items.0.description')
    },
    {
      icon: Users,
      title: t('about.values.items.1.title'),
      description: t('about.values.items.1.description')
    },
    {
      icon: Lightbulb,
      title: t('about.values.items.2.title'),
      description: t('about.values.items.2.description')
    },
    {
      icon: Target,
      title: t('about.values.items.3.title'),
      description: t('about.values.items.3.description')
    }
  ];

  return (
    <div className="pt-16">
      {/* Hero Section */}
      <section className="py-20 bg-gradient-to-br from-primary-50 to-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-8 text-center">
              {t('about.title')}
            </h1>
            <div className="max-w-4xl mx-auto space-y-6">
              <p className="text-xl text-gray-600 leading-relaxed text-justify">
                {t('about.description')}
              </p>
              <p className="text-xl text-gray-600 leading-relaxed text-justify">
                {t('about.organization')}
              </p>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Mission & Vision */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 items-start">
            <motion.div
              initial={{ opacity: 0, x: -30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
            >
              <Card className="h-full flex flex-col">
                <div className="text-center flex-shrink-0">
                  <div className="w-16 h-16 bg-primary-600 rounded-full flex items-center justify-center mx-auto mb-6">
                    <Target size={32} className="text-white" />
                  </div>
                  <h2 className="text-2xl font-bold text-gray-900 mb-4">
                    {t('about.mission.title')}
                  </h2>
                  <p className="text-xl text-gray-600 mb-6 min-h-[3.5rem] text-center">
                    {t('about.mission.text')}
                  </p>
                </div>
                <p className="text-gray-500 text-justify flex-grow">
                  {t('about.mission.description')}
                </p>
              </Card>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: -30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
            >
              <Card className="h-full flex flex-col">
                <div className="text-center flex-shrink-0">
                  <div className="w-16 h-16 bg-primary-600 rounded-full flex items-center justify-center mx-auto mb-6">
                    <Sparkles size={32} className="text-white" />
                  </div>
                  <h2 className="text-2xl font-bold text-gray-900 mb-4">
                    {t('about.vision.title')}
                  </h2>
                  <p className={`text-xl text-gray-600 mb-6 min-h-[3.5rem] text-center ${isChinese ? 'max-w-none' : '-mx-6 px-6 max-w-none whitespace-nowrap'}`}>
                    {t('about.vision.text')}
                  </p>
                </div>
                <p className="text-gray-500 text-justify flex-grow">
                  {t('about.vision.description')}
                </p>
              </Card>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: -30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
            >
              <Card className="h-full flex flex-col">
                <div className="text-center flex-shrink-0">
                  <div className="w-16 h-16 bg-primary-600 rounded-full flex items-center justify-center mx-auto mb-6">
                    <Heart size={32} className="text-white" />
                  </div>
                  <h2 className="text-2xl font-bold text-gray-900 mb-4">
                    {t('about.slogan.title')}
                  </h2>
                  <p className="text-xl text-gray-600 mb-6 min-h-[3.5rem] text-center">
                    {t('about.slogan.text')}
                  </p>
                </div>
                <p className="text-gray-500 text-justify flex-grow">
                  {t('about.slogan.description')}
                </p>
              </Card>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Company Values */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              {t('about.values.title')}
            </h2>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {values.map((value, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.1 }}
              >
                <Card className="h-full flex flex-col">
                  <div className="text-center flex-shrink-0">
                    <div className="w-16 h-16 bg-primary-600 rounded-full flex items-center justify-center mx-auto mb-6">
                      <value.icon size={32} className="text-white" />
                    </div>
                    <h3 className="text-xl font-bold text-gray-900 mb-4">
                      {value.title}
                    </h3>
                  </div>
                  <p className={`text-gray-600 flex-grow ${isChinese ? 'text-center' : 'text-justify'}`}>
                    {value.description}
                  </p>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Company Info */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-8">
              {t('about.company.title')}
            </h2>
            <div className="max-w-4xl mx-auto">
              <Card gradient className="text-left">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                  <div className="text-center">
                    <div className="text-3xl font-bold text-primary-600 mb-2">{t('about.company.established')}</div>
                    <div className="text-gray-600">{t('about.company.establishedLabel')}</div>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-primary-600 mb-2">{t('about.company.headquarters')}</div>
                    <div className="text-gray-600">{t('about.company.headquartersLabel')}</div>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-primary-600 mb-2">{t('about.company.scope')}</div>
                    <div className="text-gray-600">{t('about.company.scopeLabel')}</div>
                  </div>
                </div>
              </Card>
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  );
};

export default AboutPage;
