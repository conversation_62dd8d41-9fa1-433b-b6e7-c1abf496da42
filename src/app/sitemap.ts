import { MetadataRoute } from 'next';
import { locales } from '@/i18n';

export default function sitemap(): MetadataRoute.Sitemap {
  const baseUrl = 'https://quotitech.com';
  
  // 基础页面
  const pages = ['', '/about', '/products', '/contact'];
  
  // 为每个语言生成页面URL
  const urls: MetadataRoute.Sitemap = [];
  
  locales.forEach(locale => {
    pages.forEach(page => {
      urls.push({
        url: `${baseUrl}/${locale}${page}`,
        lastModified: new Date(),
        changeFrequency: 'weekly',
        priority: page === '' ? 1 : 0.8,
      });
    });
  });

  return urls;
}
