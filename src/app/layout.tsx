import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'Quoti Technology Limited - 让科技，务实服务日常',
  description: '夸地科技有限公司是一家应用驱动型科技公司，专注于将AI等科技能力应用于个人日常生活，解决个人用户日常的"小问题"。',
  keywords: 'Quoti Technology, 夸地科技, AI, 科技, 应用, 日常生活, UnMiss, Lantai',
  authors: [{ name: 'Quoti Technology Limited' }],
  viewport: 'width=device-width, initial-scale=1',
  robots: 'index, follow',
  openGraph: {
    title: 'Quoti Technology Limited - 让科技，务实服务日常',
    description: '应用驱动型科技公司，专注于将AI等科技能力应用于个人日常生活',
    type: 'website',
    locale: 'zh_CN',
    alternateLocale: ['en_US', 'ja_<PERSON>', 'es_ES'],
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="zh">
      <body className={inter.className}>
        {children}
      </body>
    </html>
  )
}
