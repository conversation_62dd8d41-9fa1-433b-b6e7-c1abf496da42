@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

:root {
  --background: 0 0% 100%;
  --foreground: 222.2 84% 4.9%;
  --primary: 262 83% 58%;
  --primary-foreground: 210 40% 98%;
  --secondary: 210 40% 96%;
  --secondary-foreground: 222.2 84% 4.9%;
  --muted: 210 40% 96%;
  --muted-foreground: 215.4 16.3% 46.9%;
  --accent: 210 40% 96%;
  --accent-foreground: 222.2 84% 4.9%;
  --destructive: 0 84.2% 60.2%;
  --destructive-foreground: 210 40% 98%;
  --border: 214.3 31.8% 91.4%;
  --input: 214.3 31.8% 91.4%;
  --ring: 262 83% 58%;
  --radius: 0.5rem;
}

.dark {
  --background: 222.2 84% 4.9%;
  --foreground: 210 40% 98%;
  --primary: 262 83% 58%;
  --primary-foreground: 222.2 84% 4.9%;
  --secondary: 217.2 32.6% 17.5%;
  --secondary-foreground: 210 40% 98%;
  --muted: 217.2 32.6% 17.5%;
  --muted-foreground: 215 20.2% 65.1%;
  --accent: 217.2 32.6% 17.5%;
  --accent-foreground: 210 40% 98%;
  --destructive: 0 62.8% 30.6%;
  --destructive-foreground: 210 40% 98%;
  --border: 217.2 32.6% 17.5%;
  --input: 217.2 32.6% 17.5%;
  --ring: 262 83% 58%;
}

* {
  border-color: hsl(var(--border));
}

body {
  color: hsl(var(--foreground));
  background: hsl(var(--background));
  font-family: 'Inter', sans-serif;
}

/* 科技感渐变背景 */
.tech-gradient {
  background: linear-gradient(135deg, #6200EE 0%, #3700B3 50%, #03DAC6 100%);
}

.tech-gradient-subtle {
  background: linear-gradient(135deg, rgba(98, 0, 238, 0.1) 0%, rgba(55, 0, 179, 0.05) 100%);
}

/* 科技感动画 */
.tech-glow {
  box-shadow: 0 0 20px rgba(98, 0, 238, 0.3);
}

.tech-border {
  border: 1px solid rgba(98, 0, 238, 0.2);
  background: rgba(98, 0, 238, 0.05);
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: #6200EE;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #3700B3;
}

/* 平滑滚动 */
html {
  scroll-behavior: smooth;
}
