'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import LogoOption1 from './ui/LogoOption1';
import LogoOption2 from './ui/LogoOption2';
import LogoOption3 from './ui/LogoOption3';
import LogoOption4 from './ui/LogoOption4';
import LogoOption5 from './ui/LogoOption5';

const LogoSelector: React.FC = () => {
  const [selectedLogo, setSelectedLogo] = useState(1);

  const logoOptions = [
    { id: 1, name: '方案1 - 双圆环科技风', component: LogoOption1, description: '双圆环结构，脉冲动画，经典科技感' },
    { id: 2, name: '方案2 - 六边形科技风', component: LogoOption2, description: '六边形外框，角点动画，现代几何美学' },
    { id: 3, name: '方案3 - 方形网格科技风', component: LogoOption3, description: '网格背景，方形元素，数字化风格' },
    { id: 4, name: '方案4 - 简约线条风', component: LogoOption4, description: '简约线条，渐进动画，极简主义' },
    { id: 5, name: '方案5 - 立体科技风', component: LogoOption5, description: '立体效果，渐变光晕，3D视觉' },
  ];

  return (
    <div className="fixed top-20 right-4 bg-white p-6 rounded-xl shadow-2xl border border-gray-200 z-50 max-w-sm">
      <h3 className="text-lg font-bold text-gray-900 mb-4">Logo方案选择</h3>
      
      {/* 当前选中的Logo大图 */}
      <div className="mb-6 p-4 bg-gray-50 rounded-lg text-center">
        <div className="mb-3">
          {React.createElement(logoOptions[selectedLogo - 1].component, { size: 'xl' })}
        </div>
        <p className="text-sm font-medium text-gray-900">
          {logoOptions[selectedLogo - 1].name}
        </p>
        <p className="text-xs text-gray-600 mt-1">
          {logoOptions[selectedLogo - 1].description}
        </p>
      </div>

      {/* Logo选项列表 */}
      <div className="space-y-2">
        {logoOptions.map((option) => (
          <motion.button
            key={option.id}
            onClick={() => setSelectedLogo(option.id)}
            className={`w-full p-3 rounded-lg border-2 transition-all duration-200 ${
              selectedLogo === option.id
                ? 'border-primary-600 bg-primary-50'
                : 'border-gray-200 hover:border-primary-300'
            }`}
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            <div className="flex items-center space-x-3">
              <div className="flex-shrink-0">
                {React.createElement(option.component, { size: 'sm' })}
              </div>
              <div className="text-left">
                <p className="text-sm font-medium text-gray-900">
                  {option.name}
                </p>
                <p className="text-xs text-gray-600">
                  {option.description}
                </p>
              </div>
            </div>
          </motion.button>
        ))}
      </div>

      <div className="mt-4 pt-4 border-t border-gray-200">
        <p className="text-xs text-gray-500 text-center">
          点击选择Logo方案，当前使用：方案{selectedLogo}
        </p>
      </div>
    </div>
  );
};

export default LogoSelector;
