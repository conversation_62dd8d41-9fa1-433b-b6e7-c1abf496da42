'use client';

import React from 'react';
import { useTranslations, useLocale } from 'next-intl';
import { motion } from 'framer-motion';
import { ArrowRight, Sparkles, Target, Users } from 'lucide-react';
import { useRouter } from 'next/navigation';
import Button from '@/components/ui/Button';
import Card from '@/components/ui/Card';
import { handleProductNavigation } from '@/lib/products';

const HomePage: React.FC = () => {
  const t = useTranslations();
  const router = useRouter();
  const locale = useLocale();

  // 判断是否为中文版本
  const isChinese = locale === 'zh-CN' || locale === 'zh-HK';

  return (
    <div className="pt-16">
      {/* Hero Section */}
      <section className="relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-slate-50 via-white to-primary-50/20">
        {/* 简洁的科技感背景 */}
        <div className="absolute inset-0 opacity-20">
          {/* 几何装饰元素 - 位置更加分散，不干扰内容 */}
          <div className="absolute top-10 left-5 w-16 h-16 border border-primary-200 rotate-45"></div>
          <div className="absolute top-20 right-10 w-12 h-12 border border-primary-300 rotate-12"></div>
          <div className="absolute bottom-20 left-10 w-14 h-14 bg-primary-100 rotate-45"></div>
          <div className="absolute bottom-10 right-20 w-10 h-10 border border-primary-200 rotate-45"></div>
        </div>

        {/* 微妙的粒子效果 */}
        <div className="absolute inset-0">
          {[...Array(8)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute w-1 h-1 bg-primary-300 rounded-full opacity-40"
              style={{
                left: `${20 + Math.random() * 60}%`,
                top: `${20 + Math.random() * 60}%`,
              }}
              animate={{
                y: [0, -10, 0],
                opacity: [0.2, 0.6, 0.2],
                scale: [1, 1.1, 1],
              }}
              transition={{
                duration: 6 + Math.random() * 2,
                repeat: Infinity,
                delay: i * 1,
                ease: "easeInOut"
              }}
            />
          ))}
        </div>

        <div className="relative z-10 max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            {/* 主标题 */}
            <h1 className="text-5xl md:text-7xl lg:text-8xl font-bold bg-gradient-to-r from-gray-900 via-primary-600 to-gray-900 bg-clip-text text-transparent mb-8">
              {t('hero.title')}
            </h1>

            {/* 副标题 */}
            <p className="text-2xl md:text-3xl text-gray-700 mb-8 font-medium">
              {t('hero.tagline')}
            </p>

            {/* 品牌slogan */}
            <div className="mb-12">
              <motion.div
                className="inline-flex items-center space-x-3 bg-gradient-to-r from-primary-600 to-primary-700 text-white px-8 py-4 rounded-full shadow-2xl"
                whileHover={{ scale: 1.05 }}
                transition={{ duration: 0.2 }}
              >
                <Sparkles size={24} />
                <span className="font-bold text-xl">{t('hero.slogan')}</span>
              </motion.div>
            </div>

            {/* 核心理念展示 */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12 max-w-5xl mx-auto">
              <motion.div
                className="bg-white/90 backdrop-blur-sm p-8 rounded-2xl border border-primary-200 shadow-lg"
                whileHover={{ y: -5 }}
                transition={{ duration: 0.2 }}
              >
                <div className="text-4xl font-bold text-primary-600 mb-4">{t('hero.concepts.items.0.title')}</div>
                <div className="text-lg text-gray-600 font-medium">{t('hero.concepts.items.0.subtitle')}</div>
              </motion.div>
              <motion.div
                className="bg-white/90 backdrop-blur-sm p-8 rounded-2xl border border-primary-200 shadow-lg"
                whileHover={{ y: -5 }}
                transition={{ duration: 0.2 }}
              >
                <div className="text-4xl font-bold text-primary-600 mb-4">{t('hero.concepts.items.1.title')}</div>
                <div className="text-lg text-gray-600 font-medium">{t('hero.concepts.items.1.subtitle')}</div>
              </motion.div>
              <motion.div
                className="bg-white/90 backdrop-blur-sm p-8 rounded-2xl border border-primary-200 shadow-lg"
                whileHover={{ y: -5 }}
                transition={{ duration: 0.2 }}
              >
                <div className="text-4xl font-bold text-primary-600 mb-4">{t('hero.concepts.items.2.title')}</div>
                <div className="text-lg text-gray-600 font-medium">{t('hero.concepts.items.2.subtitle')}</div>
              </motion.div>
            </div>

            <Button
              size="lg"
              className="group px-8 py-4 text-lg"
              onClick={() => router.push('/about')}
            >
              {t('hero.cta')}
              <ArrowRight size={24} className="ml-3 group-hover:translate-x-1 transition-transform" />
            </Button>
          </motion.div>
        </div>
      </section>

      {/* Company Mission & Vision */}
      <section className="py-24 bg-gradient-to-br from-primary-50 to-white">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              {t('hero.concepts.title')}
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              {t('hero.concepts.subtitle')}
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-12">
            <motion.div
              initial={{ opacity: 0, x: -30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
            >
              <Card className="h-full p-8 bg-white/80 backdrop-blur-sm border-2 border-primary-200">
                <div className="w-20 h-20 bg-gradient-to-br from-primary-600 to-primary-700 rounded-2xl flex items-center justify-center mx-auto mb-8">
                  <Target size={40} className="text-white" />
                </div>
                <h3 className="text-3xl font-bold text-gray-900 mb-6 text-center">
                  {t('about.mission.title')}
                </h3>
                <p className="text-2xl text-primary-600 font-semibold text-center leading-tight">
                  {t('about.mission.text')}
                </p>
              </Card>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
            >
              <Card className="h-full p-8 bg-white/80 backdrop-blur-sm border-2 border-primary-200">
                <div className="w-20 h-20 bg-gradient-to-br from-primary-600 to-primary-700 rounded-2xl flex items-center justify-center mx-auto mb-8">
                  <Users size={40} className="text-white" />
                </div>
                <h3 className="text-3xl font-bold text-gray-900 mb-6 text-center">
                  {t('about.vision.title')}
                </h3>
                <p className="text-2xl text-primary-600 font-semibold text-center leading-tight">
                  {t('about.vision.text')}
                </p>
              </Card>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Products Preview */}
      <section className="py-24 bg-gray-50">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              {t('products.title')}
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              {t('products.subtitle')}
            </p>
          </motion.div>

          {/* 兰台系列介绍 */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="mb-16"
          >
            <Card className="p-12 bg-gradient-to-br from-primary-600 to-primary-700 text-white text-center">
              <h3 className="text-4xl font-bold mb-6">
                {t('products.lantai.title')}
              </h3>
              <p className="text-xl mb-4 opacity-90">
                {t('products.lantai.subtitle')}
              </p>
              <p className={`text-lg opacity-80 max-w-3xl mx-auto leading-relaxed ${isChinese ? 'text-center' : 'text-left'}`}>
                {t('products.lantai.homeDescription')}
              </p>
            </Card>
          </motion.div>

          {/* UnMiss 产品卡片 */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
          >
            <Card className="p-12 bg-white border-2 border-primary-200 relative overflow-hidden">
              <div className="absolute top-6 right-6">
                <span className="bg-gradient-to-r from-primary-600 to-primary-700 text-white px-4 py-2 rounded-full text-sm font-medium shadow-lg">
                  {t('products.unmiss.status')}
                </span>
              </div>

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                <div>
                  <h3 className="text-4xl font-bold text-gray-900 mb-4">
                    {t('products.unmiss.title')}
                  </h3>
                  <p className="text-2xl text-primary-600 font-semibold mb-6">
                    {t('products.unmiss.subtitle')}
                  </p>
                  <p className="text-lg text-gray-600 mb-8 leading-relaxed">
                    {t('products.unmiss.description')}
                  </p>
                  <Button
                    size="lg"
                    className="group px-8 py-4 text-lg"
                    onClick={() => handleProductNavigation('unmiss')}
                  >
                    {t('products.unmiss.cta')}
                    <ArrowRight size={20} className="ml-3 group-hover:translate-x-1 transition-transform" />
                  </Button>
                </div>

                <div className="flex items-center justify-center">
                  <div className="w-64 h-64 bg-gradient-to-br from-primary-100 to-primary-200 rounded-3xl flex items-center justify-center">
                    <div className="text-6xl font-bold text-primary-600">UnMiss</div>
                  </div>
                </div>
              </div>
            </Card>
          </motion.div>
        </div>
      </section>
    </div>
  );
};

export default HomePage;
