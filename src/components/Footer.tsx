'use client';

import React from 'react';
import { useTranslations } from 'next-intl';
import { useRouter } from 'next/navigation';
import Logo from '@/components/ui/Logo';

const Footer: React.FC = () => {
  const t = useTranslations('footer');
  const tNav = useTranslations('nav');
  const router = useRouter();

  return (
    <footer className="bg-gray-900 text-white py-12">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Company Info */}
          <div className="col-span-1 md:col-span-2">
            <div className="flex items-center space-x-3 mb-4">
              <Logo size="sm" />
              <span className="font-bold text-xl">Quoti Technology</span>
            </div>
            <p className="text-gray-400 mb-4 max-w-md">
              {t('description')}
            </p>
            <p className="text-gray-500 text-sm">
              © 2025 {t('company')}. {t('rights')}.
            </p>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="font-semibold mb-4">{t('quickLinks')}</h3>
            <ul className="space-y-2 text-gray-400">
              <li><button onClick={() => router.push('/')} className="hover:text-white transition-colors text-left">{tNav('home')}</button></li>
              <li><button onClick={() => router.push('/about')} className="hover:text-white transition-colors text-left">{tNav('about')}</button></li>
              <li><button onClick={() => router.push('/products')} className="hover:text-white transition-colors text-left">{tNav('products')}</button></li>
              <li><button onClick={() => router.push('/contact')} className="hover:text-white transition-colors text-left">{tNav('contact')}</button></li>
            </ul>
          </div>

          {/* Contact */}
          <div>
            <h3 className="font-semibold mb-4">{t('contactInfo')}</h3>
            <div className="space-y-2 text-gray-400">
              <p><EMAIL></p>
            </div>
          </div>
        </div>

        <div className="border-t border-gray-800 mt-8 pt-8 text-center text-gray-500">
          <p>{t('slogan')}</p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
