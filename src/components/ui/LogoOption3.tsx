'use client';

import React from 'react';
import { motion } from 'framer-motion';

interface LogoProps {
  size?: 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
}

const LogoOption3: React.FC<LogoProps> = ({ size = 'md', className = '' }) => {
  const sizeClasses = {
    sm: 'w-8 h-8',
    md: 'w-12 h-12',
    lg: 'w-16 h-16',
    xl: 'w-24 h-24'
  };

  return (
    <motion.div
      className={`relative ${sizeClasses[size]} ${className}`}
      whileHover={{ scale: 1.05 }}
      transition={{ duration: 0.2 }}
    >
      <svg
        width="100%"
        height="100%"
        viewBox="0 0 48 48"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        {/* 外框 */}
        <rect
          x="4"
          y="4"
          width="40"
          height="40"
          rx="8"
          stroke="#6200EE"
          strokeWidth="2"
          fill="rgba(98, 0, 238, 0.05)"
        />
        
        {/* 网格线 */}
        <line x1="16" y1="4" x2="16" y2="44" stroke="#6200EE" strokeWidth="0.5" className="opacity-30" />
        <line x1="32" y1="4" x2="32" y2="44" stroke="#6200EE" strokeWidth="0.5" className="opacity-30" />
        <line x1="4" y1="16" x2="44" y2="16" stroke="#6200EE" strokeWidth="0.5" className="opacity-30" />
        <line x1="4" y1="32" x2="44" y2="32" stroke="#6200EE" strokeWidth="0.5" className="opacity-30" />
        
        {/* Q字母主体 */}
        <circle
          cx="24"
          cy="24"
          r="12"
          stroke="#6200EE"
          strokeWidth="3"
          fill="none"
        />
        
        {/* Q字母尾巴 - 数字化风格 */}
        <path
          d="M30 30L36 36M33 33L36 30"
          stroke="#6200EE"
          strokeWidth="3"
          strokeLinecap="round"
        />
        
        {/* 四角装饰方块 */}
        <motion.rect
          x="6" y="6" width="4" height="4" rx="1"
          fill="#6200EE"
          animate={{ opacity: [0.3, 1, 0.3] }}
          transition={{ duration: 2, repeat: Infinity, delay: 0 }}
        />
        <motion.rect
          x="38" y="6" width="4" height="4" rx="1"
          fill="#6200EE"
          animate={{ opacity: [0.3, 1, 0.3] }}
          transition={{ duration: 2, repeat: Infinity, delay: 0.5 }}
        />
        <motion.rect
          x="6" y="38" width="4" height="4" rx="1"
          fill="#6200EE"
          animate={{ opacity: [0.3, 1, 0.3] }}
          transition={{ duration: 2, repeat: Infinity, delay: 1 }}
        />
        <motion.rect
          x="38" y="38" width="4" height="4" rx="1"
          fill="#6200EE"
          animate={{ opacity: [0.3, 1, 0.3] }}
          transition={{ duration: 2, repeat: Infinity, delay: 1.5 }}
        />
        
        {/* 中心装饰点 */}
        <circle cx="24" cy="24" r="2" fill="#6200EE" className="opacity-80" />
      </svg>
    </motion.div>
  );
};

export default LogoOption3;
