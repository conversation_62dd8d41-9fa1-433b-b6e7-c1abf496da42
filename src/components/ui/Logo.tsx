'use client';

import React from 'react';
import { motion } from 'framer-motion';

interface LogoProps {
  size?: 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
}

const Logo: React.FC<LogoProps> = ({ size = 'md', className = '' }) => {
  const sizeClasses = {
    sm: 'w-8 h-8',
    md: 'w-12 h-12',
    lg: 'w-16 h-16',
    xl: 'w-24 h-24'
  };

  return (
    <motion.div
      className={`relative ${sizeClasses[size]} ${className}`}
      whileHover={{ scale: 1.05 }}
      transition={{ duration: 0.2 }}
    >
      <svg
        width="100%"
        height="100%"
        viewBox="0 0 220 220"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <defs>
          <linearGradient id="brandGrad" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor="#6200EE"/>
            <stop offset="100%" stopColor="#B388FF"/>
          </linearGradient>
        </defs>

        {/* Q 圆环 */}
        <circle
          cx="100"
          cy="100"
          r="80"
          fill="none"
          stroke="url(#brandGrad)"
          strokeWidth="40"
          strokeLinecap="round"
        />

        {/* 4 个像素块（Q的尾巴设计） */}
        <rect x="145" y="145" width="24" height="24" fill="url(#brandGrad)"/>
        <rect x="177" y="145" width="16" height="16" fill="url(#brandGrad)"/>
        <rect x="145" y="177" width="16" height="16" fill="url(#brandGrad)"/>
        <rect x="165" y="170" width="10" height="10" fill="url(#brandGrad)"/>
      </svg>
    </motion.div>
  );
};

export default Logo;
