'use client';

import React from 'react';
import { motion } from 'framer-motion';

interface LogoProps {
  size?: 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
}

const LogoOption2: React.FC<LogoProps> = ({ size = 'md', className = '' }) => {
  const sizeClasses = {
    sm: 'w-8 h-8',
    md: 'w-12 h-12',
    lg: 'w-16 h-16',
    xl: 'w-24 h-24'
  };

  return (
    <motion.div
      className={`relative ${sizeClasses[size]} ${className}`}
      whileHover={{ scale: 1.05, rotate: 5 }}
      transition={{ duration: 0.3 }}
    >
      <svg
        width="100%"
        height="100%"
        viewBox="0 0 48 48"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        {/* 六边形外框 */}
        <motion.path
          d="M24 4L36 12L36 28L24 36L12 28L12 12Z"
          stroke="#6200EE"
          strokeWidth="2"
          fill="rgba(98, 0, 238, 0.1)"
          initial={{ pathLength: 0 }}
          animate={{ pathLength: 1 }}
          transition={{ duration: 2, repeat: Infinity, repeatType: "reverse" }}
        />
        
        {/* 内部六边形 */}
        <path
          d="M24 8L32 14L32 26L24 32L16 26L16 14Z"
          stroke="#6200EE"
          strokeWidth="1.5"
          fill="none"
          className="opacity-60"
        />
        
        {/* Q字母 - 现代化设计 */}
        <path
          d="M18 20C18 16.7 20.7 14 24 14C27.3 14 30 16.7 30 20C30 23.3 27.3 26 24 26C20.7 26 18 23.3 18 20Z"
          stroke="#6200EE"
          strokeWidth="2.5"
          fill="none"
          strokeLinecap="round"
        />
        
        {/* Q字母尾巴 - 科技感设计 */}
        <path
          d="M27 23L30 26L32 24"
          stroke="#6200EE"
          strokeWidth="2.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        
        {/* 六个角的装饰点 */}
        <motion.circle 
          cx="24" cy="4" r="1" fill="#6200EE"
          animate={{ scale: [1, 1.5, 1] }}
          transition={{ duration: 2, repeat: Infinity, delay: 0 }}
        />
        <motion.circle 
          cx="36" cy="12" r="1" fill="#6200EE"
          animate={{ scale: [1, 1.5, 1] }}
          transition={{ duration: 2, repeat: Infinity, delay: 0.3 }}
        />
        <motion.circle 
          cx="36" cy="28" r="1" fill="#6200EE"
          animate={{ scale: [1, 1.5, 1] }}
          transition={{ duration: 2, repeat: Infinity, delay: 0.6 }}
        />
        <motion.circle 
          cx="24" cy="36" r="1" fill="#6200EE"
          animate={{ scale: [1, 1.5, 1] }}
          transition={{ duration: 2, repeat: Infinity, delay: 0.9 }}
        />
        <motion.circle 
          cx="12" cy="28" r="1" fill="#6200EE"
          animate={{ scale: [1, 1.5, 1] }}
          transition={{ duration: 2, repeat: Infinity, delay: 1.2 }}
        />
        <motion.circle 
          cx="12" cy="12" r="1" fill="#6200EE"
          animate={{ scale: [1, 1.5, 1] }}
          transition={{ duration: 2, repeat: Infinity, delay: 1.5 }}
        />
      </svg>
    </motion.div>
  );
};

export default LogoOption2;
