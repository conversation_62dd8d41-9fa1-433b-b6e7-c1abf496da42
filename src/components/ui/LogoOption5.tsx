'use client';

import React from 'react';
import { motion } from 'framer-motion';

interface LogoProps {
  size?: 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
}

const LogoOption5: React.FC<LogoProps> = ({ size = 'md', className = '' }) => {
  const sizeClasses = {
    sm: 'w-8 h-8',
    md: 'w-12 h-12',
    lg: 'w-16 h-16',
    xl: 'w-24 h-24'
  };

  return (
    <motion.div
      className={`relative ${sizeClasses[size]} ${className}`}
      whileHover={{ scale: 1.05, rotateY: 15 }}
      transition={{ duration: 0.3 }}
      style={{ perspective: '1000px' }}
    >
      <svg
        width="100%"
        height="100%"
        viewBox="0 0 48 48"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <defs>
          <linearGradient id="qGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor="#6200EE" />
            <stop offset="50%" stopColor="#3700B3" />
            <stop offset="100%" stopColor="#6200EE" />
          </linearGradient>
          <filter id="glow">
            <feGaussianBlur stdDeviation="2" result="coloredBlur"/>
            <feMerge> 
              <feMergeNode in="coloredBlur"/>
              <feMergeNode in="SourceGraphic"/>
            </feMerge>
          </filter>
        </defs>
        
        {/* 立体底座 */}
        <ellipse
          cx="24" cy="40"
          rx="18" ry="4"
          fill="rgba(98, 0, 238, 0.2)"
        />
        
        {/* Q字母主体 - 立体效果 */}
        <motion.circle
          cx="24" cy="22"
          r="14"
          stroke="url(#qGradient)"
          strokeWidth="4"
          fill="rgba(98, 0, 238, 0.1)"
          filter="url(#glow)"
          animate={{
            strokeWidth: [4, 5, 4],
          }}
          transition={{
            duration: 2,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
        
        {/* Q字母尾巴 - 立体设计 */}
        <motion.path
          d="M32 30L38 36L40 34"
          stroke="url(#qGradient)"
          strokeWidth="4"
          strokeLinecap="round"
          strokeLinejoin="round"
          filter="url(#glow)"
          animate={{
            strokeWidth: [4, 5, 4],
          }}
          transition={{
            duration: 2,
            repeat: Infinity,
            delay: 0.5,
            ease: "easeInOut"
          }}
        />
        
        {/* 立体装饰元素 */}
        <motion.circle
          cx="24" cy="8" r="2"
          fill="url(#qGradient)"
          animate={{
            r: [2, 3, 2],
            opacity: [0.6, 1, 0.6],
          }}
          transition={{
            duration: 1.5,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
        
        <motion.circle
          cx="8" cy="22" r="1.5"
          fill="url(#qGradient)"
          animate={{
            r: [1.5, 2.5, 1.5],
            opacity: [0.6, 1, 0.6],
          }}
          transition={{
            duration: 1.5,
            repeat: Infinity,
            delay: 0.3,
            ease: "easeInOut"
          }}
        />
        
        <motion.circle
          cx="40" cy="22" r="1.5"
          fill="url(#qGradient)"
          animate={{
            r: [1.5, 2.5, 1.5],
            opacity: [0.6, 1, 0.6],
          }}
          transition={{
            duration: 1.5,
            repeat: Infinity,
            delay: 0.6,
            ease: "easeInOut"
          }}
        />
      </svg>
    </motion.div>
  );
};

export default LogoOption5;
