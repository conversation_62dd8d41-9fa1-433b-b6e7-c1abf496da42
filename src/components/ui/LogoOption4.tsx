'use client';

import React from 'react';
import { motion } from 'framer-motion';

interface LogoProps {
  size?: 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
}

const LogoOption4: React.FC<LogoProps> = ({ size = 'md', className = '' }) => {
  const sizeClasses = {
    sm: 'w-8 h-8',
    md: 'w-12 h-12',
    lg: 'w-16 h-16',
    xl: 'w-24 h-24'
  };

  return (
    <motion.div
      className={`relative ${sizeClasses[size]} ${className}`}
      whileHover={{ scale: 1.05 }}
      transition={{ duration: 0.2 }}
    >
      <svg
        width="100%"
        height="100%"
        viewBox="0 0 48 48"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        {/* Q字母主体 - 简约风格 */}
        <motion.path
          d="M14 24C14 17.4 19.4 12 26 12C32.6 12 38 17.4 38 24C38 30.6 32.6 36 26 36C19.4 36 14 30.6 14 24Z"
          stroke="#6200EE"
          strokeWidth="4"
          fill="none"
          strokeLinecap="round"
          initial={{ pathLength: 0 }}
          animate={{ pathLength: 1 }}
          transition={{ duration: 1.5, ease: "easeInOut" }}
        />
        
        {/* Q字母尾巴 - 简约设计 */}
        <motion.path
          d="M32 32L40 40"
          stroke="#6200EE"
          strokeWidth="4"
          strokeLinecap="round"
          initial={{ pathLength: 0 }}
          animate={{ pathLength: 1 }}
          transition={{ duration: 1.5, delay: 0.5, ease: "easeInOut" }}
        />
        
        {/* 装饰线条 */}
        <motion.line
          x1="8" y1="24" x2="12" y2="24"
          stroke="#6200EE"
          strokeWidth="2"
          strokeLinecap="round"
          initial={{ opacity: 0, x: -10 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.8, delay: 1 }}
        />
        <motion.line
          x1="40" y1="24" x2="44" y2="24"
          stroke="#6200EE"
          strokeWidth="2"
          strokeLinecap="round"
          initial={{ opacity: 0, x: 10 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.8, delay: 1.2 }}
        />
        <motion.line
          x1="26" y1="8" x2="26" y2="4"
          stroke="#6200EE"
          strokeWidth="2"
          strokeLinecap="round"
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 1.4 }}
        />
        <motion.line
          x1="26" y1="40" x2="26" y2="44"
          stroke="#6200EE"
          strokeWidth="2"
          strokeLinecap="round"
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 1.6 }}
        />
      </svg>
    </motion.div>
  );
};

export default LogoOption4;
