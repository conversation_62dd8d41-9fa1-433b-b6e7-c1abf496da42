'use client';

import React from 'react';
import { motion } from 'framer-motion';

interface LogoProps {
  size?: 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
}

const LogoOption1: React.FC<LogoProps> = ({ size = 'md', className = '' }) => {
  const sizeClasses = {
    sm: 'w-8 h-8',
    md: 'w-12 h-12',
    lg: 'w-16 h-16',
    xl: 'w-24 h-24'
  };

  return (
    <motion.div
      className={`relative ${sizeClasses[size]} ${className}`}
      whileHover={{ scale: 1.05 }}
      transition={{ duration: 0.2 }}
    >
      <svg
        width="100%"
        height="100%"
        viewBox="0 0 48 48"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        {/* 外圆环 */}
        <circle
          cx="24"
          cy="24"
          r="20"
          stroke="#6200EE"
          strokeWidth="2"
          fill="none"
          className="opacity-80"
        />
        
        {/* 内圆环 */}
        <circle
          cx="24"
          cy="24"
          r="15"
          stroke="#6200EE"
          strokeWidth="1.5"
          fill="none"
          className="opacity-60"
        />
        
        {/* Q字母的主体部分 */}
        <path
          d="M16 24C16 19.6 19.6 16 24 16C28.4 16 32 19.6 32 24C32 28.4 28.4 32 24 32C19.6 32 16 28.4 16 24Z"
          stroke="#6200EE"
          strokeWidth="3"
          fill="none"
          strokeLinecap="round"
        />
        
        {/* Q字母的尾巴 */}
        <path
          d="M28 28L32 32"
          stroke="#6200EE"
          strokeWidth="3"
          strokeLinecap="round"
        />
        
        {/* 科技感装饰点 */}
        <circle cx="24" cy="12" r="1.5" fill="#6200EE" className="opacity-70" />
        <circle cx="36" cy="24" r="1.5" fill="#6200EE" className="opacity-70" />
        <circle cx="24" cy="36" r="1.5" fill="#6200EE" className="opacity-70" />
        <circle cx="12" cy="24" r="1.5" fill="#6200EE" className="opacity-70" />
        
        {/* 动态脉冲效果 */}
        <motion.circle
          cx="24"
          cy="24"
          r="22"
          stroke="#6200EE"
          strokeWidth="1"
          fill="none"
          className="opacity-30"
          animate={{
            r: [22, 24, 22],
            opacity: [0.3, 0.1, 0.3],
          }}
          transition={{
            duration: 2,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
      </svg>
    </motion.div>
  );
};

export default LogoOption1;
