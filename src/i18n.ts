import { getRequestConfig } from 'next-intl/server';
import { notFound } from 'next/navigation';

// 支持的语言列表
export const locales = [
  'en',    // English
  'zh-CN', // 简体中文
  'zh-HK', // 繁體中文（香港）
] as const;

export type Locale = (typeof locales)[number];

export default getRequestConfig(async ({ locale }) => {
  // 验证传入的语言是否支持
  if (!locales.includes(locale as Locale)) notFound();

  return {
    messages: (await import(`./messages/${locale}.json`)).default
  };
});
