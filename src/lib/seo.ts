import { Metadata } from 'next';

export interface SEOConfig {
  title: string;
  description: string;
  keywords: string[];
  ogImage?: string;
  canonical?: string;
}

// 基础SEO配置
export const baseSEO: SEOConfig = {
  title: 'Quoti Technology Limited - 让科技，务实服务日常',
  description: '夸地科技有限公司是一家应用驱动型科技公司，专注于将AI等科技能力应用于个人日常生活，解决个人用户日常的"小问题"。',
  keywords: [
    'Quoti Technology',
    '夸地科技',
    'AI',
    '科技',
    '应用',
    '日常生活',
    'UnMiss',
    'Lantai',
    '重要信息管理',
    '智能助手',
    '香港科技公司'
  ]
};

// 页面特定的SEO配置
export const pageSEO = {
  home: {
    title: 'Quoti Technology - 让科技，务实服务日常',
    description: '夸地科技有限公司，应用驱动型科技公司，专注于将AI等科技能力应用于个人日常生活，用科技帮助1亿人解决1千个"小问题"。',
    keywords: [...baseSEO.keywords, '首页', '科技公司', '人工智能']
  },
  about: {
    title: '关于我们 - Quoti Technology',
    description: '了解夸地科技的使命愿景、公司定位和价值观。我们致力于让科技务实服务日常，用科技帮助1亿人解决1千个"小问题"。',
    keywords: [...baseSEO.keywords, '关于我们', '公司介绍', '使命愿景']
  },
  products: {
    title: '产品中心 - Quoti Technology',
    description: '探索夸地科技的创新产品，包括兰台系列重要信息管理产品UnMiss等，为您的日常生活提供智能解决方案。',
    keywords: [...baseSEO.keywords, '产品', '兰台系列', 'UnMiss', '信息管理']
  }
};

// 生成页面元数据
export function generatePageMetadata(
  page: keyof typeof pageSEO,
  locale: string = 'zh'
): Metadata {
  const seo = pageSEO[page];
  
  return {
    title: seo.title,
    description: seo.description,
    keywords: seo.keywords.join(', '),
    authors: [{ name: 'Quoti Technology Limited' }],
    creator: 'Quoti Technology Limited',
    publisher: 'Quoti Technology Limited',
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        'max-video-preview': -1,
        'max-image-preview': 'large',
        'max-snippet': -1,
      },
    },
    openGraph: {
      type: 'website',
      locale: locale,
      url: `https://quotitech.com/${locale}`,
      title: seo.title,
      description: seo.description,
      siteName: 'Quoti Technology Limited',
      images: [
        {
          url: '/og-image.jpg',
          width: 1200,
          height: 630,
          alt: 'Quoti Technology Limited',
        },
      ],
    },
    twitter: {
      card: 'summary_large_image',
      title: seo.title,
      description: seo.description,
      images: ['/og-image.jpg'],
    },
    alternates: {
      canonical: `https://quotitech.com/${locale}`,
      languages: {
        'zh-CN': 'https://quotitech.com/zh',
        'en-US': 'https://quotitech.com/en',
        'ja-JP': 'https://quotitech.com/ja',
        'es-ES': 'https://quotitech.com/es',
      },
    },
    verification: {
      google: 'your-google-verification-code',
      yandex: 'your-yandex-verification-code',
      yahoo: 'your-yahoo-verification-code',
    },
  };
}

// 结构化数据
export const structuredData = {
  organization: {
    '@context': 'https://schema.org',
    '@type': 'Organization',
    name: 'Quoti Technology Limited',
    alternateName: '夸地科技有限公司',
    url: 'https://quotitech.com',
    logo: 'https://quotitech.com/logo.png',
    description: '应用驱动型科技公司，专注于将AI等科技能力应用于个人日常生活',
    foundingDate: '2024',
    foundingLocation: {
      '@type': 'Place',
      name: 'Hong Kong'
    },
    sameAs: [
      // 社交媒体链接将来可以添加
    ]
  },
  website: {
    '@context': 'https://schema.org',
    '@type': 'WebSite',
    name: 'Quoti Technology Limited',
    url: 'https://quotitech.com',
    description: '夸地科技有限公司官方网站',
    inLanguage: ['zh-CN', 'en-US', 'ja-JP', 'es-ES'],
    potentialAction: {
      '@type': 'SearchAction',
      target: 'https://quotitech.com/search?q={search_term_string}',
      'query-input': 'required name=search_term_string'
    }
  }
};
