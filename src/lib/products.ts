// 产品配置和跳转逻辑

export interface Product {
  id: string;
  name: string;
  series: string;
  status: 'coming-soon' | 'beta' | 'live';
  url?: string;
  description: string;
  features: string[];
  launchDate?: string;
}

export const products: Product[] = [
  {
    id: 'unmiss',
    name: 'UnMiss',
    series: 'Lantai',
    status: 'coming-soon',
    url: undefined, // 将来设置为实际的产品网站URL
    description: 'Never miss what matters - 重要信息管理的智能助手',
    features: [
      '智能信息捕获和分类',
      '个性化提醒和通知',
      '多平台数据同步',
      '隐私保护和数据安全'
    ],
    launchDate: '2025-Q3'
  }
  // 未来可以添加更多产品
];

export const productSeries = {
  lantai: {
    name: 'Lantai Series',
    nameZh: '兰台系列',
    description: 'Important Information Management Series',
    descriptionZh: '重要信息管理系列',
    products: products.filter(p => p.series === 'Lantai')
  }
};

// 产品跳转处理函数
export const handleProductNavigation = (productId: string) => {
  const product = products.find(p => p.id === productId);
  
  if (!product) {
    console.error(`Product with id ${productId} not found`);
    return;
  }

  switch (product.status) {
    case 'live':
      if (product.url) {
        // 跳转到产品网站
        window.open(product.url, '_blank');
      } else {
        alert('产品网站即将上线，敬请期待！');
      }
      break;
    
    case 'beta':
      if (product.url) {
        // 跳转到测试版本
        window.open(product.url, '_blank');
      } else {
        alert('产品测试版即将开放，敬请期待！');
      }
      break;
    
    case 'coming-soon':
    default:
      // 显示即将推出的消息
      alert(`${product.name} 正在紧张开发中，敬请期待！\n预计发布时间：${product.launchDate || '待定'}`);
      break;
  }
};

// 获取产品状态显示文本
export const getProductStatusText = (status: Product['status'], locale: string = 'zh') => {
  const statusTexts = {
    'coming-soon': {
      zh: '即将推出',
      en: 'Coming Soon',
      ja: '近日公開',
      es: 'Próximamente'
    },
    'beta': {
      zh: '测试版',
      en: 'Beta',
      ja: 'ベータ版',
      es: 'Beta'
    },
    'live': {
      zh: '正式版',
      en: 'Live',
      ja: '正式版',
      es: 'En Vivo'
    }
  };

  return statusTexts[status][locale as keyof typeof statusTexts[typeof status]] || statusTexts[status]['en'];
};

// 检查产品是否可以访问
export const isProductAccessible = (productId: string): boolean => {
  const product = products.find(p => p.id === productId);
  return product ? product.status === 'live' || product.status === 'beta' : false;
};

// 获取产品URL（如果可访问）
export const getProductUrl = (productId: string): string | null => {
  const product = products.find(p => p.id === productId);
  return product && isProductAccessible(productId) ? product.url || null : null;
};
