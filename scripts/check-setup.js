#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

console.log('🔍 检查项目配置...\n');

// 检查必要文件
const requiredFiles = [
  'package.json',
  'next.config.js',
  'tailwind.config.ts',
  'tsconfig.json',
  'src/app/layout.tsx',
  'src/app/[locale]/layout.tsx',
  'src/app/[locale]/page.tsx',
  'src/i18n.ts',
  'src/middleware.ts'
];

let allFilesExist = true;

console.log('📁 检查必要文件:');
requiredFiles.forEach(file => {
  const exists = fs.existsSync(file);
  console.log(`${exists ? '✅' : '❌'} ${file}`);
  if (!exists) allFilesExist = false;
});

// 检查语言文件
console.log('\n🌍 检查语言文件:');
const languages = ['en', 'zh', 'ja', 'es', 'fr', 'ko'];
languages.forEach(lang => {
  const file = `src/messages/${lang}.json`;
  const exists = fs.existsSync(file);
  console.log(`${exists ? '✅' : '❌'} ${file}`);
  if (!exists) allFilesExist = false;
});

// 检查组件文件
console.log('\n🧩 检查组件文件:');
const components = [
  'src/components/Navigation.tsx',
  'src/components/Footer.tsx',
  'src/components/ui/Button.tsx',
  'src/components/ui/Card.tsx',
  'src/components/pages/HomePage.tsx'
];

components.forEach(file => {
  const exists = fs.existsSync(file);
  console.log(`${exists ? '✅' : '❌'} ${file}`);
  if (!exists) allFilesExist = false;
});

// 检查package.json依赖
console.log('\n📦 检查依赖配置:');
try {
  const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
  const requiredDeps = [
    'next',
    'react',
    'react-dom',
    'next-intl',
    'framer-motion',
    'lucide-react',
    'tailwindcss'
  ];
  
  requiredDeps.forEach(dep => {
    const exists = packageJson.dependencies && packageJson.dependencies[dep];
    console.log(`${exists ? '✅' : '❌'} ${dep}`);
    if (!exists) allFilesExist = false;
  });
} catch (error) {
  console.log('❌ 无法读取 package.json');
  allFilesExist = false;
}

console.log('\n' + '='.repeat(50));
if (allFilesExist) {
  console.log('🎉 项目配置检查通过！');
  console.log('\n📋 下一步操作:');
  console.log('1. 安装 Node.js (如果尚未安装)');
  console.log('2. 运行 npm install 安装依赖');
  console.log('3. 运行 npm run dev 启动开发服务器');
  console.log('4. 访问 http://localhost:3000 查看网站');
} else {
  console.log('❌ 项目配置存在问题，请检查缺失的文件');
}
console.log('='.repeat(50));
