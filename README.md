# QuotiTechnology 官方网站

夸地科技有限公司官方网站 - 让科技，更实用

## 🌟 项目简介

QuotiTechnology官方网站是一个现代化、响应式的企业官网，专为夸地科技打造。网站全面展示了公司"让科技，更实用"的核心使命，以及运用AI等科技能力解决个人日常生活中"小问题"的业务定位。

## 🏢 公司信息

- **公司全称**: 夸地科技有限公司 (Quoti Technology Limited)
- **成立时间**: 2025年
- **全球总部**: 香港特别行政区
- **公司使命**: 让科技，更实用
- **公司愿景**: 1亿人的生活小帮手
- **品牌理念**: 小问题，大温暖
- **公司价值观**: 以善为先、用户是我、技术为用、简单务实

## ⚡ 技术栈

- **框架**: Next.js 14 (App Router)
- **语言**: TypeScript
- **样式**: Tailwind CSS
- **国际化**: next-intl
- **部署**: Vercel

## ✨ 功能特性

- 🌐 **多语言支持**: 简体中文、繁体中文（香港）、英文
- 📱 **响应式设计**: 完美适配桌面、平板、手机
- 🎨 **专业设计**: 简洁现代的UI设计，紫色主题 (#6200EE)
- 🔍 **SEO优化**: 完整的SEO配置和站点地图
- 🚀 **高性能**: 快速加载和流畅的用户体验

## 🚀 快速开始

### 环境要求
- Node.js 18+
- npm 或 yarn

### 安装步骤

1. **克隆仓库**
```bash
git clone https://github.com/QuotiTechnology/QuotiTechnology-website.git
cd QuotiTechnology-website
```

2. **安装依赖**
```bash
npm install
```

3. **启动开发服务器**
```bash
npm run dev
```

4. **访问网站**
打开浏览器访问 [http://localhost:3000](http://localhost:3000)

### 可用脚本

```bash
npm run dev          # 启动开发服务器
npm run build        # 构建生产版本
npm run start        # 启动生产服务器
npm run lint         # 代码检查
```

### 构建和部署

1. **构建生产版本**
   ```bash
   npm run build
   # 或者
   yarn build
   ```

2. **启动生产服务器**
   ```bash
   npm run start
   # 或者
   yarn start
   ```

## 📁 项目结构

```
src/
├── app/
│   ├── [locale]/          # 多语言路由
│   │   ├── about/         # 关于我们页面
│   │   ├── products/      # 产品页面
│   │   ├── contact/       # 联系我们页面
│   │   └── page.tsx       # 首页
│   ├── globals.css        # 全局样式
│   ├── layout.tsx         # 根布局
│   ├── sitemap.ts         # 站点地图
│   └── robots.ts          # 搜索引擎配置
├── components/
│   ├── ui/               # UI组件
│   │   ├── Logo.tsx      # Logo组件
│   │   ├── Button.tsx    # 按钮组件
│   │   └── Card.tsx      # 卡片组件
│   ├── Navigation.tsx    # 导航组件
│   ├── Footer.tsx        # 页脚组件
│   └── LanguageSwitcher.tsx # 语言切换器
├── messages/             # 国际化文件
│   ├── zh-CN.json       # 简体中文
│   ├── zh-HK.json       # 繁体中文
│   └── en.json          # 英文
├── lib/                 # 工具函数
│   ├── products.ts      # 产品配置
│   └── seo.ts          # SEO配置
├── i18n.ts             # 国际化配置
└── middleware.ts       # 中间件
```

## 🌐 多语言支持

网站支持三种语言：
- **简体中文** (`/zh-CN/`)
- **繁体中文** (`/zh-HK/`)
- **英文** (`/en/`)

语言切换功能会自动检测用户的系统语言，同时支持手动切换。

## 🚀 部署

### Vercel部署（推荐）
1. 连接GitHub仓库到Vercel
2. 自动部署和预览

### 手动部署
```bash
npm run build
npm run start
```

## 📄 页面结构

### 首页 (`/`)
- 公司使命展示："让科技，更实用"
- 业务定位介绍：用AI等科技能力解决生活中的"小问题"
- 业务特点展示：科技为本、解决小问题、服务全球
- "兰台"系列产品概览

### 关于我们 (`/about`)
- **公司使命**：让科技，更实用
- **公司愿景**：1亿人的生活小帮手
- **品牌理念**：小问题，大温暖
- **公司价值观**：以善为先、用户是我、技术为用、简单务实
- **公司背景**：2025年成立，全球总部位于香港

### 产品页面 (`/products`)
- **"兰台"系列**：专注于重要信息管理的产品系列
- **UnMiss产品**：帮助用户不再错过重要信息，目前正在开发中，预计2025年Q3发布
- **系列规划**：更多"兰台"系列信息管理解决方案正在规划中
- **未来产品**：将开发更多解决日常生活"小问题"的实用产品

### 联系我们 (`/contact`)
- **联系方式**：邮箱 <EMAIL>
- **联系内容**：产品咨询、技术支持、意见反馈等
- **回复时间**：我们会在3个工作日内回复
- **隐私保护**：您的联系信息将被严格保密

## 📞 联系方式

- **邮箱**: <EMAIL>

## 📄 许可证

© 2025 夸地科技有限公司. 版权所有.

---

**夸地科技** - 让科技，更实用
